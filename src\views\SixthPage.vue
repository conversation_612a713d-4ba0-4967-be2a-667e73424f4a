<template>
  <div class="sixth-page-container">
    <div class="header-container">
      <div header-container>
        <h1 class="subscription-text">立即订阅</h1>
        <h4 class="small-text">Subscribe Now</h4>
      </div>
    </div>
    <div class="subcription-year">
      <div class="year-option single" :class="{ active: selectedYear === '1年' }" @click="selectYear('1年')">
        1年
      </div>
      <div class="year-option single" :class="{ active: selectedYear === '2年' }" @click="selectYear('2年')">
        2年
      </div>
      <div class="year-option double" :class="{ active: selectedYear === '3年' }" @click="selectYear('3年')">
        <span>3年</span>
        <span class="discount">-20%</span>
      </div>
    </div>
    <div class="price-container">
      <div v-for="card in priceCards" :key="card.id" class="price-card">
        <div class="top-portion">
          <span class="price-heard-text">{{ card.topText }}</span>
          <img src="../assets/pricecard_topstart_left.svg" alt="topstart_left" class="topstart-left" />
          <img src="../assets/pricecard_topstart_right.svg" alt="topstart_right" class="topstart-right" />
        </div>
        <div class="bottom-portion">
          <div class="package-contents">
            <span class="package-text">{{ card.packageName }}</span>
            <span class="package-sub-text">{{ card.packageNameEn }}</span>
            <span class="price-text">{{ card.price }}<span class="year-text">{{ card.priceUnit }}</span></span>
            <el-button type="primary" size="large" class="experience-btn">
              立即体验
            </el-button>
          </div>
          <div class="package-describe">
            <span
              v-for="(feature, index) in card.features"
              :key="index"
              class="describe-title"
            >
              <img :src="feature.icon" width="18px" height="18px"/>
              <span class="describe-text">{{ feature.text }}</span>
            </span>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 默认选中年份
const selectedYear = ref<string>('1年')

const selectYear = (year: string) => {
  selectedYear.value = year
  console.log('选中年份:', year)
}

// 价格卡片数据类型定义
interface PriceCard {
  id: number
  topText: string
  packageName: string
  packageNameEn: string
  price: string
  priceUnit: string
  // buttonText: string
  features: Array<{
    icon: string
    text: string
  }>
}

// 价格卡片数据
const priceCards = ref<PriceCard[]>([
  {
    id: 1,
    topText: '限时免费 抢先体验!',
    packageName: '钻石会员',
    packageNameEn: 'Diamond Member',
    price: '¥0',
    priceUnit: '/年',
    // buttonText: '立即体验',
    features: [
      { icon: '/src/assets/price_bottom_first.svg', text: 'Unlimiter User' },
      { icon: '/src/assets/price_bottom_second.svg', text: 'Email & Live Support' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Keyword Finder' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Common Keywords' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Competitor Research' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Backlink Research' },
      { icon: '/src/assets/price_bottom_third.svg', text: '500 Research Credits' },
      { icon: '/src/assets/price_bottom_third.svg', text: '100 Results per Credit' }
    ]
  },
  {
    id: 2,
    topText: '限时免费 抢先体验!',
    packageName: '钻石会员',
    packageNameEn: 'Diamond Member',
    price: '¥0',
    priceUnit: '/年',
    // buttonText: '立即体验',
    features: [
      { icon: '/src/assets/price_bottom_first.svg', text: 'Unlimiter User' },
      { icon: '/src/assets/price_bottom_second.svg', text: 'Email & Live Support' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Keyword Finder' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Common Keywords' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Competitor Research' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Backlink Research' },
      { icon: '/src/assets/price_bottom_third.svg', text: '500 Research Credits' },
      { icon: '/src/assets/price_bottom_third.svg', text: '100 Results per Credit' }
    ]
  },
  {
    id: 3,
    topText: '限时免费 抢先体验!',
    packageName: '钻石会员',
    packageNameEn: 'Diamond Member',
    price: '¥0',
    priceUnit: '/年',
    // buttonText: '立即体验',
    features: [
      { icon: '/src/assets/price_bottom_first.svg', text: 'Unlimiter User' },
      { icon: '/src/assets/price_bottom_second.svg', text: 'Email & Live Support' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Keyword Finder' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Common Keywords' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Competitor Research' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Backlink Research' },
      { icon: '/src/assets/price_bottom_third.svg', text: '500 Research Credits' },
      { icon: '/src/assets/price_bottom_third.svg', text: '100 Results per Credit' }
    ]
  },
  {
    id: 4,
    topText: '限时免费 抢先体验!',
    packageName: '钻石会员',
    packageNameEn: 'Diamond Member',
    price: '¥0',
    priceUnit: '/年',
    // buttonText: '立即体验',
    features: [
      { icon: '/src/assets/price_bottom_first.svg', text: 'Unlimiter User' },
      { icon: '/src/assets/price_bottom_second.svg', text: 'Email & Live Support' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Keyword Finder' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Common Keywords' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Competitor Research' },
      { icon: '/src/assets/price_bottom_third.svg', text: 'Backlink Research' },
      { icon: '/src/assets/price_bottom_third.svg', text: '500 Research Credits' },
      { icon: '/src/assets/price_bottom_third.svg', text: '100 Results per Credit' }
    ]
  }
])
</script>

<style scoped>
.sixth-page-container {
  width: calc(100% - 48px);
  height: calc(1144px - 48px);
  margin: 0px 24px 0px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 120px;
  background-image: url("../assets/fixthpage_background.png");
  border-radius: 20px;
  margin-bottom: 96px;
}

.subscription-text {
  padding-top: 80px;
  margin-bottom: 0px;
  color: var(--text-white, #FFF);
  text-align: center;
  font-family: "江城斜黑体", sans-serif;
  font-size: 48px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  letter-spacing: 4.8px;
}

.small-text {
  margin-top: 14px;
  color: var(--text-4, #C9CDD4);
  text-align: center;
  font-family: JiangChengXieHei;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 1.6px;
  margin-bottom: 80px;
}

.subcription-year {
  width: 270px;
  height: 40px;
  padding: 6px;
  border-radius: 10px;
  border: 1px solid var(--border-2, #E5E6EB);
  background: var(--text-white, #FFF);
  backdrop-filter: blur(1.5px);
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 80px;
}

.year-option {
  height: 42px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-3, #86909C);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 2px;
}

.year-option.single {
  flex: 1;
}

.year-option.double {
  flex: 2;
  gap: 8px;
}

.year-option.active {
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.20);
  background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%);
  color: #ffffff;
}

.discount {
  border-radius: 699px;
  border: 1px solid rgba(255, 255, 255, 0.04);
  background: rgba(47, 125, 251, 0.10);
  backdrop-filter: blur(2px);
  color: #2F7DFB;
  text-align: center;
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.year-option.active .discount {
  color: white;
}

.year-option:hover:not(.active) {
  background: #f5f5f5;
}

.price-container {
  display: flex;
  width: 1440px;
  height: 603px;
  align-items: flex-end;
  gap: 24px;
  padding: 0; /* 清除默认内边距 */
  margin: 0 auto; /* 水平居中 */
}

.price-container > div {
  flex: 1;
  height: 100%;
  /* background: rgba(47, 125, 251, 0.10); */
  backdrop-filter: blur(10px);
}

.top-portion {
  border-radius: 20px 20px 0 0;
  background: radial-gradient(86.86% 86.86% at 50% 111.67%, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.00) 100%), #2F7DFB;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(47, 125, 251, 0.00);
}

.price-heard-text {
  margin-top: -20px;
  text-align: center;
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  letter-spacing: 2px;
  text-transform: uppercase;
  background: linear-gradient(180deg, #FFF 22.5%, rgba(255, 255, 255, 0.70) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.topstart-left {
  position: absolute;
  margin: 2px 260px 31px 51px;
}

.topstart-right {
  position: absolute;
  margin: 13px 72px 20px 228px;
}

.bottom-portion {
  border-radius: 20px;
  height: 547px;
  margin-top: -20px;
  border: 1.5px solid rgba(47, 125, 251, 0.50);
  background: linear-gradient(180deg, #FFF 0%, rgba(255, 255, 255, 0.80) 100%), #2F7DFB;
}

.package-contents {
  display: flex;
  flex-direction: column;
  margin: 24px;
  position: relative;
}

.package-contents::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    rgba(47, 125, 251, 0.10) 0%,
    rgba(47, 125, 251, 0.50) 50%,
    rgba(47, 125, 251, 0.10) 100%
  );
}

.package-text {
  color: var(--text-1, #1D2129);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 24px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
}

.package-sub-text {
  color: var(--text-4, #C9CDD4);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding-bottom: 16px;
}

.price-text {
  color: var(--text-1, #1D2129);
  font-family: "MiSans VF";
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.year-text {
  color: rgba(29, 33, 41, 0.40);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.experience-btn {
  margin-top: 24px;
  width: 104px;
  height: 40px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.10);
  background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%);
  margin-bottom: 24px;
}

.package-describe {
  margin: 0 0 0 24px;
  display: flex;
  flex-direction: column;
}

.describe-title {
  display: flex;
  padding-bottom: 8px;
}

.describe-text {
  padding-left: 8px;
  color: var(--text-2, #4E5969);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
</style>