<template>
  <div class="fifth-page-container">
    <div class="header-container">
      <h1 class="header-text">
        <span class="service-text">服务</span>体验
      </h1>
      <h4 class="small-text">
        Service Experience
      </h4>
    </div>
    <div class="service-experience">
      <div
        v-for="category in serviceCategories"
        :key="category.key"
        class="service-item"
        :class="{ active: selectedCategory === category.key }"
        @click="selectCategory(category.key)"
      >
        <img :src="category.icon" width="22px" height="22px" :alt="category.name"/>
        <span class="item-text">{{ category.name }}</span>
      </div>
    </div>
    <div class="service-sub-item">
      <!-- 桌面端布局 -->
      <div class="desktop-layout">
        <div class="left-container" ref="leftContainer">
          <div
            v-for="(item, index) in currentLeftItems"
            :key="item.id"
            class="left-item"
            :class="{ active: selectedLeftItem === index }"
            @click="selectLeftItem(index)"
          >
            <img :src="item.icon" width="36px" height="36px"/>
            <span>{{ item.title }}</span>
            <p>{{ item.description }}</p>
          </div>
        </div>
        <div class="right-item">
          <img :src="currentRightImage" width="1084px" height="692px"/>
          <el-button
            class="experience-btn"
            @mouseenter="showQRCode = true"
            @mouseleave="showQRCode = false"
          >
            <img src="@/assets/ServiceExperience/Two-dimensional.svg"
              width="18px" height="18px" style="margin-right: 4px;" >扫码体验
          </el-button>
          <div
            v-if="showQRCode && currentQRCode"
            class="qr-code-popup"
            @mouseenter="showQRCode = true"
            @mouseleave="showQRCode = false"
          >
            <img :src="currentQRCode" alt="二维码" width="140px" height="140px"/>
          </div>
        </div>
      </div>

      <!-- 移动端手风琴布局 -->
      <div class="mobile-accordion">
        <div
          v-for="(item, index) in currentLeftItems"
          :key="item.id"
          class="mobile-accordion-item"
        >
          <!-- 手风琴标题栏 -->
          <div
            class="mobile-accordion-header"
            @click="toggleMobileItem(index)"
          >
            <div class="mobile-accordion-title">
              <img :src="item.icon" width="24px" height="24px"/>
              <span>{{ item.title }}</span>
            </div>
            <div class="mobile-accordion-arrow">
              {{ expandedMobileItem === index ? '▼' : '▲' }}
            </div>
          </div>

          <!-- 手风琴内容区域 - 显示在对应项目下面 -->
          <div
            v-if="expandedMobileItem === index"
            class="mobile-accordion-content"
          >
            <div class="mobile-accordion-image">
              <img :src="rightImagesData[selectedCategory]?.[index]?.image" alt="服务图片"/>
            </div>
            <el-button class="mobile-experience-btn">
              立即体验
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'

// 服务类别数据类型
interface ServiceCategory {
  key: string
  name: string
  icon: string
}

// 左侧项目数据类型
interface LeftItem {
  id: string
  title: string
  icon: string
  description: string
}

// 右侧图片数据类型
interface RightImage {
  id: string
  image: string
}

// 服务类别数据
const serviceCategories: ServiceCategory[] = [
  {
    key: 'house',
    name: '住',
    icon: '/src/assets/ServiceExperience/house.svg'
  },
  {
    key: 'eat',
    name: '食',
    icon: '/src/assets/ServiceExperience/eat.svg'
  },
  {
    key: 'clothing',
    name: '衣',
    icon: '/src/assets/ServiceExperience/clothing.svg'
  },
  {
    key: 'trip',
    name: '行',
    icon: '/src/assets/ServiceExperience/trip.svg'
  }
]

// 左侧项目数据
const leftItemsData: Record<string, LeftItem[]> = {
  house: [
    {
      id: 'house-1',
      title: '楼盘云-SAPP',
      icon: '/src/assets/ServiceExperience/left_house.svg',
      description: '将传统业务进行转型，以数据为驱动提升业务效率和改变业务模式'
    },
    {
      id: 'house-2',
      title: '设计云',
      icon: '/src/assets/ServiceExperience/design_cloud.svg',
      description: '将传统业务进行转型，以数据为驱动提升业务效率和改变业务模式'
    },
    {
      id: 'house-3',
      title: '研发云',
      icon: '/src/assets/ServiceExperience/RD_cloud.svg',
      description: '将传统业务进行转型，以数据为驱动提升业务效率和改变业务模式'
    },
    {
      id: 'house-3',
      title: '研发云',
      icon: '/src/assets/ServiceExperience/RD_cloud.svg',
      description: '将传统业务进行转型，以数据为驱动提升业务效率和改变业务模式'
    },
    {
      id: 'house-3',
      title: '研发云',
      icon: '/src/assets/ServiceExperience/RD_cloud.svg',
      description: '将传统业务进行转型，以数据为驱动提升业务效率和改变业务模式'
    }
  ],
  eat: [
    {
      id: 'eat-1',
      title: '餐饮管理系统',
      icon: '/src/assets/ServiceExperience/restaurant_system.svg',
      description: '智能餐饮管理解决方案，提升餐厅运营效率'
    },
    {
      id: 'eat-2',
      title: '外卖配送平台',
      icon: '/src/assets/ServiceExperience/delivery_platform.svg',
      description: '高效的外卖配送管理系统，优化配送路线'
    }
  ],
  clothing: [
    {
      id: 'clothing-1',
      title: '时尚设计平台',
      icon: '/src/assets/ServiceExperience/fashion_design.svg',
      description: '创新的时尚设计工具，助力设计师创作'
    }
  ],
  trip: [
    {
      id: 'trip-1',
      title: '智能出行助手',
      icon: '/src/assets/ServiceExperience/smart_travel.svg',
      description: '智能化出行规划，提供最优路线推荐'
    }
  ]
}

// 右侧图片数据
const rightImagesData: Record<string, RightImage[]> = {
  house: [
    { id: 'house-img-1', image: '/src/assets/ServiceExperience/house_main_picture.png' },
    { id: 'house-img-2', image: '/src/assets/ServiceExperience/house_design_picture.png' },
    { id: 'house-img-3', image: '/src/assets/ServiceExperience/house_rd_picture.png' }
  ],
  eat: [
    { id: 'eat-img-1', image: '/src/assets/ServiceExperience/restaurant_picture.png' },
    { id: 'eat-img-2', image: '/src/assets/ServiceExperience/delivery_picture.png' }
  ],
  clothing: [
    { id: 'clothing-img-1', image: '/src/assets/ServiceExperience/fashion_picture.png' }
  ],
  trip: [
    { id: 'trip-img-1', image: '/src/assets/ServiceExperience/travel_picture.png' }
  ]
}

const qrCodeData: Record<string, string> = {
  // 住房相关二维码
  'house-img-1': '/src/assets/ServiceExperience/QR/house_QR.png',
  'house-img-2': '/src/assets/ServiceExperience/QR/clothing.svg',
  'house-img-3': '/src/assets/ServiceExperience/QR/house_rd_qr.png',

  // 餐饮相关二维码
  'eat-img-1': '/src/assets/ServiceExperience/QR/restaurant_qr.png',
  'eat-img-2': '/src/assets/ServiceExperience/QR/delivery_qr.png',

  // 服装相关二维码
  'clothing-img-1': '/src/assets/ServiceExperience/QR/fashion_qr.png',

  // 出行相关二维码
  'trip-img-1': '/src/assets/ServiceExperience/QR/travel_qr.png'
}

// 选中类别
const selectedCategory = ref<string>('house') // 默认选中住

// 选中的左侧项目索引
const selectedLeftItem = ref<number>(0) // 默认选中第一个

const showQRCode = ref<boolean>(false)

// left-container的引用
const leftContainer = ref<HTMLElement | null>(null)

// 移动端手风琴展开状态 - 记录当前展开的项目索引
const expandedMobileItem = ref<number | null>(null)

// 当前类别的左侧项目
const currentLeftItems = computed(() => {
  return leftItemsData[selectedCategory.value] || []
})

// 当前显示的右侧图片
const currentRightImage = computed(() => {
  const images = rightImagesData[selectedCategory.value] || []
  return images[selectedLeftItem.value]?.image || '../assets/ServiceExperience/house_main_picture.png'
})

// 当前显示的二维码
const currentQRCode = computed(() => {
  const images = rightImagesData[selectedCategory.value] || []
  const currentImageId = images[selectedLeftItem.value]?.id
  return currentImageId ? qrCodeData[currentImageId] : null
})

// 选择类别的方法
const selectCategory = (categoryKey: string) => {
  selectedCategory.value = categoryKey
  selectedLeftItem.value = 0 // 切换类别时重置到第一个左侧项目
  expandedMobileItem.value = null // 切换类别时重置移动端展开状态
}

// 选择左侧项目的方法
const selectLeftItem = (index: number) => {
  selectedLeftItem.value = index
}

// 移动端切换手风琴展开状态的方法
const toggleMobileItem = (index: number) => {
  // 如果点击的是当前展开的项目，则折叠它
  if (expandedMobileItem.value === index) {
    expandedMobileItem.value = null
  } else {
    // 否则展开点击的项目
    expandedMobileItem.value = index
  }
}

// 处理滚动事件，添加模糊效果
const handleScroll = () => {
  if (!leftContainer.value) return

  const container = leftContainer.value
  const items = container.querySelectorAll('.left-item')
  const containerRect = container.getBoundingClientRect()
  const containerTop = containerRect.top
  const containerBottom = containerRect.bottom
  const fadeDistance = 60 // 渐变距离

  items.forEach((item) => {
    const itemRect = item.getBoundingClientRect()
    const itemTop = itemRect.top
    const itemBottom = itemRect.bottom

    let opacity = 1
    let blur = 0

    // 顶部渐变 - 当卡片顶部超出容器顶部时
    if (itemTop < containerTop) {
      const hiddenHeight = containerTop - itemTop
      if (hiddenHeight > 0) {
        const ratio = Math.max(0, 1 - hiddenHeight / fadeDistance)
        opacity = Math.max(0.4, ratio) // 最小透明度0.4，确保能看到
        blur = (1 - ratio) * 1.5
      }
    }

    // 底部渐变 - 当卡片底部超出容器底部时
    if (itemBottom > containerBottom) {
      const hiddenHeight = itemBottom - containerBottom
      if (hiddenHeight > 0) {
        const ratio = Math.max(0, 1 - hiddenHeight / fadeDistance)
        opacity = Math.min(opacity, Math.max(0.5, ratio)) // 底部最小透明度0.5，更明显
        blur = Math.max(blur, (1 - ratio) * 1.5)
      }
    }

    // 应用样式
    const element = item as HTMLElement
    element.style.opacity = opacity.toString()
    element.style.filter = `blur(${blur}px)`
    element.style.transition = 'opacity 0.2s ease, filter 0.2s ease'
  })
}

// 组件挂载后设置滚动监听
onMounted(() => {
  nextTick(() => {
    if (leftContainer.value) {
      leftContainer.value.addEventListener('scroll', handleScroll)
      handleScroll() // 初始化
    }
  })
})
</script>
<style scoped>
.fifth-page-container {
  width: calc(100% - 48px);
  margin: 0px 24px 0px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 120px;
}

.header-text {
  padding-top: 120px;
  margin-bottom: 0px;
  color: var(--text-1, #1D2129);
  font-family: "江城斜黑体", sans-serif;
  font-size: 48px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  letter-spacing: 4.8px;
}

.service-text {
  color: #00d8c1;
}

.small-text {
  margin-top: 0;
  color: var(--text-4, #C9CDD4);
  text-align: center;
  font-family: JiangChengXieHei;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 1.6px;
  margin-bottom: 80px;
}

.service-experience {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 31px;
  margin-bottom: 40px;
}

.item-text {
  color: var(--text-4, #C9CDD4);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 20px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 98px;
  height: 48px;
  margin: 10px 0;
  padding: 0 24px;
  border-radius: 10px;
  box-sizing: border-box;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.service-item:hover {
  background-color: #f0f0f0;
}

.service-item.active {
  background-color: #edf3ff;

}

.service-item.active .item-text {
  color: #2f7dfb;
}

.service-item.active img {
  filter: brightness(0) saturate(100%) invert(27%) sepia(99%) saturate(2834%) hue-rotate(214deg) brightness(101%) contrast(97%);
}

.service-sub-item {
  width: 1440px;
  height: 692px;
  display: flex;
  /* gap: 24px; */
}

.desktop-layout {
  display: flex;
  width: 100%;
  height: 100%;
}

.mobile-accordion {
  display: none;
}

.left-container {
  display: flex;
  flex-direction: column;
  width: 340px;
  height: 692px;
  flex-shrink: 0;
  margin-right: 18px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  padding-right: 8px; /* 添加右侧内边距确保边框显示 */
  box-sizing: border-box;
}

/* 隐藏滚动条 - Webkit */
.left-container::-webkit-scrollbar {
  display: none;
}

.left-item {
  height: 160px;
  width: 332px;
  border: 1px solid var(--border-2, #E5E6EB);
  display: flex;
  flex-direction: column;
  border-radius: 20px;
  background-color: #ffffff;
  margin-bottom: 24px;
  padding: 24px 0 0 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  /* 确保边框完整显示 */
  margin-right: 0;
}

.left-item:last-child {
  margin-bottom: 0;
}

.left-item.active {
  background-color: #f2f3f5;
}

.left-item > span {
  color: var(--text-1, #1D2129);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 20px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  margin-bottom: 10px;
}

.left-item > p {
  margin-top: 0px;
  overflow: hidden;
  color: var(--text-3, #86909C);
  text-overflow: ellipsis;
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  width: 268px;
  height: 48px;
}

.right-item {
  flex-shrink: 0;
  position: relative;
}

.right-item > img {
  border-radius: 20px;
}

.experience-btn {
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.10);
  background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%);
  width: 126px;
  height: 40px;
  text-align: center;
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  position: absolute;
  bottom: 48px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.experience-btn:hover {
  background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.10) !important;
  transform: translateX(-50%) !important;
}

:deep(.experience-btn span) {
  background: linear-gradient(180deg, #FFF 19.23%, rgba(255, 255, 255, 0.60) 79.23%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.qr-code-popup {
  position: absolute;
  bottom: 48px;
  left: calc(50% + 80px); /* 按钮右边 */
  width: 140px;
  height: 140px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
  border: 1px solid #e5e6eb;
  animation: fadeIn 0.2s ease-out;
}

.qr-code-popup img {
  border-radius: 4px;
  width: 140px;
  height: 140px;
  object-fit: cover;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@font-face {
  font-family: "Alibaba PuHuiTi 2.0";
  src: url("../assets/typeface/Alibaba_PuHuiTi_2.0_65_Medium_65_Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .fifth-page-container {
    width: calc(100% - 24px);
    margin: 0px 12px 0px 12px;
    margin-bottom: 40px;
  }

  .header-text {
    padding-top: 60px;
    font-size: 32px;
    letter-spacing: 3.2px;
    text-align: center;
  }

  .small-text {
    font-size: 14px;
    letter-spacing: 1.4px;
    margin-bottom: 40px;
  }

  .service-experience {
    gap: 16px;
    margin-bottom: 24px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .service-item {
    width: 80px;
    height: 40px;
    padding: 0 16px;
    border-radius: 8px;
  }

  .item-text {
    font-size: 16px;
  }

  .service-sub-item {
    width: 100%;
    height: auto;
    flex-direction: column;
    gap: 20px;
  }

  /* 隐藏桌面端布局 */
  .desktop-layout {
    display: none;
  }

  /* 显示移动端手风琴 */
  .mobile-accordion {
    display: block;
    width: 100%;
  }

  .mobile-accordion-item {
    margin-bottom: 16px;
    border: 1px solid var(--border-2, #E5E6EB);
    border-radius: 16px;
    background-color: #ffffff;
    overflow: hidden;
  }

  .mobile-accordion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .mobile-accordion-header:hover {
    background-color: #f8f9fa;
  }

  .mobile-accordion-title {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .mobile-accordion-title span {
    color: var(--text-1, #1D2129);
    font-family: "Alibaba PuHuiTi 2.0";
    font-size: 18px;
    font-weight: 900;
    line-height: normal;
  }

  .mobile-accordion-arrow {
    color: var(--text-3, #86909C);
    font-size: 14px;
    transition: transform 0.3s ease;
  }

  .mobile-accordion-content {
    padding: 0 20px 20px 20px;
    border-top: 1px solid var(--border-2, #E5E6EB);
    animation: slideDown 0.3s ease-out;
  }

  .mobile-accordion-image {
    text-align: center;
    margin: 16px 0;
  }

  .mobile-accordion-image img {
    width: 100%;
    height: 570px;
    object-fit: cover;
    border-radius: 12px;
  }

  .mobile-experience-btn {
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.10);
    background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%);
    width: 140px;
    height: 44px;
    text-align: center;
    font-family: "Alibaba PuHuiTi 2.0";
    font-size: 16px;
    font-weight: 500;
    margin: 16px auto 0;
    display: block;
  }

  .mobile-experience-btn:hover {
    background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.10) !important;
  }

  :deep(.mobile-experience-btn span) {
    background: linear-gradient(180deg, #FFF 19.23%, rgba(255, 255, 255, 0.60) 79.23%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* 移动端禁用悬停效果 */
  .service-item:hover {
    background-color: transparent;
  }

  .service-item.active {
    background-color: #edf3ff;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 500px;
  }
}

/* 小屏手机适配 */
@media (max-width: 480px) {
  .fifth-page-container {
    width: calc(100% - 16px);
    margin: 0px 8px 0px 8px;
  }

  .header-text {
    padding-top: 40px;
    font-size: 28px;
    letter-spacing: 2.8px;
  }

  .small-text {
    font-size: 12px;
    letter-spacing: 1.2px;
    margin-bottom: 32px;
  }

  .service-experience {
    gap: 12px;
    margin-bottom: 20px;
  }

  .service-item {
    width: 70px;
    height: 36px;
    padding: 0 12px;
    border-radius: 6px;
  }

  .service-item img {
    width: 18px;
    height: 18px;
  }

  .item-text {
    font-size: 14px;
  }

  .mobile-accordion-item {
    margin-bottom: 12px;
    border-radius: 12px;
  }

  .mobile-accordion-header {
    padding: 16px;
  }

  .mobile-accordion-title {
    gap: 10px;
  }

  .mobile-accordion-title img {
    width: 20px;
    height: 20px;
  }

  .mobile-accordion-title span {
    font-size: 16px;
  }

  .mobile-accordion-content {
    padding: 0 16px 16px 16px;
  }

  .mobile-accordion-image {
    margin: 12px 0;
  }

  .mobile-accordion-image img {
    height: 480px;
    border-radius: 10px;
  }

  .mobile-experience-btn {
    width: 120px;
    height: 40px;
    font-size: 14px;
    margin: 12px auto 0;
  }
}

/* 超小屏适配 */
@media (max-width: 360px) {
  .fifth-page-container {
    width: calc(100% - 12px);
    margin: 0px 6px 0px 6px;
  }

  .header-text {
    padding-top: 32px;
    font-size: 24px;
    letter-spacing: 2.4px;
  }

  .small-text {
    font-size: 11px;
    letter-spacing: 1.1px;
    margin-bottom: 28px;
  }

  .service-experience {
    gap: 8px;
    margin-bottom: 16px;
  }

  .service-item {
    width: 60px;
    height: 32px;
    padding: 0 8px;
    border-radius: 4px;
  }

  .service-item img {
    width: 16px;
    height: 16px;
  }

  .item-text {
    font-size: 12px;
  }

  .mobile-accordion-item {
    margin-bottom: 10px;
    border-radius: 10px;
  }

  .mobile-accordion-header {
    padding: 12px;
  }

  .mobile-accordion-title {
    gap: 8px;
  }

  .mobile-accordion-title img {
    width: 18px;
    height: 18px;
  }

  .mobile-accordion-title span {
    font-size: 14px;
  }

  .mobile-accordion-arrow {
    font-size: 12px;
  }

  .mobile-accordion-content {
    padding: 0 12px 12px 12px;
  }

  .mobile-accordion-image {
    margin: 10px 0;
  }

  .mobile-accordion-image img {
    height: 400px;
    border-radius: 8px;
  }

  .mobile-experience-btn {
    width: 100px;
    height: 36px;
    font-size: 12px;
    margin: 10px auto 0;
  }
}

</style>