<template>
  <div class="fourth-page-container">
    <div class="header-container">
      <h1 class="header-text">
        <span class="app-text">应用</span>服务
      </h1>
      <h4 class="small-text">
        Application Services
      </h4>
    </div>
    <div class="service-container">
      <div
        class="service-item"
        :class="{ active: selectedService === 'consult' }"
        @click="selectService('consult')"
      >
        <img :src="getServiceIcon('consult')" width="22px" height="22px" alt="咨询服务" />
        <span class="service-text">咨询服务</span>
      </div>
      <div
        class="service-item"
        :class="{ active: selectedService === 'technology' }"
        @click="selectService('technology')"
      >
        <img :src="getServiceIcon('technology')" width="22px" height="22px" alt="技术服务" />
        <span class="service-text">技术服务</span>
      </div>
      <div
        class="service-item"
        :class="{ active: selectedService === 'model' }"
        @click="selectService('model')"
      >
        <img :src="getServiceIcon('model')" width="22px" height="22px" alt="模型服务" />
        <span class="service-text">模型服务</span>
      </div>
      <div
        class="service-item"
        :class="{ active: selectedService === 'financial' }"
        @click="selectService('financial')"
      >
        <img :src="getServiceIcon('financial')" width="22px" height="22px" alt="金融服务" />
        <span class="service-text">金融服务</span>
      </div>
    </div>
    <div class="service-sub-container">
      <div
        class="service-sub-item"
        :style="{
          transform: isMobile ? `translateX(-${currentMobileCardIndex * (100 / allServiceCards.length)}%)` : `translateX(-${currentPage * (342 + 24) * 4}px)`,
          '--total-cards': allServiceCards.length
        }"
      >
        <div v-for="(card, index) in allServiceCards" :key="card.id" class="service-card">
          <div class="card-sub">
            <div class="card-header">
              <img :src="card.icon" width="60px" height="60px" :alt="card.title" class="card-icon">
              <div class="card-content">
                <div class="card-title">
                  <h3>{{ card.title }}</h3>
                </div>
                <div class="card-description">
                  <p>{{ card.description }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="totalPages > 1" class="pagination-container">
      <img
        src="../assets/left.svg"
        width="40px"
        height="40px"
        class="page-turning"
        :class="{ disabled: !canGoPrevious }"
        @click="goToPreviousPage"
      />
      <img
        src="../assets/right.svg"
        width="40px"
        height="40px"
        class="page-turning-right"
        :class="{ disabled: !canGoNext }"
        @click="goToNextPage"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 服务卡片数据类型
interface ServiceCard {
  id: string
  title: string
  icon: string
  description: string
}

// 服务数据
const serviceData: Record<string, ServiceCard[]> = {
  consult: [
    {
      id: 'consult-1',
      title: '业务数字化咨询',
      icon: '/src/assets/AppService/Business_figures_mr.svg',
      description: '将传统业务进行转型，以数据为驱动提升业务效率和改变业务模式。'
    },
    {
      id: 'consult-2',
      title: '商业模式咨询',
      icon: '/src/assets/AppService/business_model_mr.svg',
      description: '采用的策略、结构、流程、盈利方式以及与其他利益相关者的关系等方面所构成的整体框架。'
    },
    {
      id: 'consult-3',
      title: '技术方案咨询',
      icon: '/src/assets/AppService/technical_proposal.svg',
      description: '从企业业务数字化到数字化业务的IT、DT，OT信息技术和数字技术，以及运营技术的规划。从工业化信息化时代转型进化升级到未来数字经济数字科技时代，AI（人工智能）、Blokechina（区块链）、Cloud（云服务）、Digitization（数字化）、Iot（物联网）协同企业业务发展战略同步科技战略开发引导企业形成具有优势的新质生产力和人工智能的应用'
    },
    {
      id: 'consult-4',
      title: '技术指标咨询',
      icon: '/src/assets/AppService/performance_indicator_mr.svg',
      description: '数字化转型的综合进度，内容、成果、成本，效益指标成熟度建设'
    },
    {
      id: 'consult-5',
      title: '投资管理咨询',
      icon: '/src/assets/AppService/investment_management.svg',
      description: '企业服务、财务、金融、商业模式、陪跑、孵化一站式数字化服务'
    }
  ],
  technology: [
    {
      id: 'tech-1',
      title: 'PLM',
      icon: '/src/assets/AppService/PLM.svg',
      description: '提供前端、后端、移动端全栈开发服务，采用最新技术栈，确保系统稳定性和可扩展性。'
    },
    {
      id: 'tech-2',
      title: 'ERP',
      icon: '/src/assets/AppService/ERP.svg',
      description: '设计高可用、高性能的系统架构，包括微服务架构、云原生架构和分布式系统设计。'
    },
    {
      id: 'tech-3',
      title: 'SCM',
      icon: '/src/assets/AppService/SCM.svg',
      description: '提供技术选型建议、代码审查、性能优化和技术培训等专业技术咨询服务。'
    },
    {
      id: 'tech-4',
      title: 'WMS',
      icon: '/src/assets/AppService/WMS.svg',
      description: '提供技术选型建议、代码审查、性能优化和技术培训等专业技术咨询服务。'
    },
    {
      id: 'tech-5',
      title: 'MES',
      icon: '/src/assets/AppService/MES.svg',
      description: '提供技术选型建议、代码审查、性能优化和技术培训等专业技术咨询服务。'
    },
    {
      id: 'tech-6',
      title: 'OA',
      icon: '/src/assets/AppService/OA.svg',
      description: '提供技术选型建议、代码审查、性能优化和技术培训等专业技术咨询服务。'
    },
    {
      id: 'tech-7',
      title: 'APS',
      icon: '/src/assets/AppService/APS.svg',
      description: '提供技术选型建议、代码审查、性能优化和技术培训等专业技术咨询服务。'
    }
  ],
  model: [
    {
      id: 'model-1',
      title: '数字建模',
      icon: '/src/assets/AppService/digital_modeling.svg',
      description: '根据订单业务数字化，供应链数字化和产业链数字化的元数据和数据源根据管理决策和经营决策建立动态数据模型'
    },
    {
      id: 'model-2',
      title: '数字影子',
      icon: '/src/assets/AppService/digital_shadow.svg',
      description: '根据业务动态数据血缘以及数据关系提炼数据要素设置映射与实体业务同步的影子数据，提供治理和应用的有效数值参数设置'
    }
  ],
  financial: [
    {
      id: 'financial-1',
      title: '消费金融',
      icon: '/src/assets/AppService/consume_finance.svg',
      description: '各大银行综合个人消费贷'
    },
    {
      id: 'financial-2',
      title: '产业金融',
      icon: '/src/assets/AppService/industrial_finance.svg',
      description: 'CIC、CVC、供应链金融'
    },
    {
      id: 'financial-3',
      title: '风险金融',
      icon: '/src/assets/AppService/risk_finance.svg',
      description: 'VC、PE、IPO'
    }
  ]
}

const selectedService = ref<string>('consult')

// 当前页码
const currentPage = ref<number>(0)

// 移动端检测
const isMobile = ref<boolean>(false)

// 移动端当前卡片索引
const currentMobileCardIndex = ref<number>(0)

// 每页显示的卡片数量
const cardsPerPage = 4

// 当前选中服务的所有卡片数据
const allServiceCards = computed(() => {
  return serviceData[selectedService.value] || []
})

// 总页数
const totalPages = computed(() => {
  if (isMobile.value) {
    return allServiceCards.value.length
  }
  return Math.ceil(allServiceCards.value.length / cardsPerPage)
})

// 判断是否可以向前翻页
const canGoPrevious = computed(() => {
  if (isMobile.value) {
    return currentMobileCardIndex.value > 0
  }
  return currentPage.value > 0
})

// 判断是否可以向后翻页
const canGoNext = computed(() => {
  if (isMobile.value) {
    return currentMobileCardIndex.value < allServiceCards.value.length - 1
  }
  return currentPage.value < totalPages.value - 1
})

// 选择服务的方法
const selectService = (serviceType: string) => {
  selectedService.value = serviceType
  currentPage.value = 0
  currentMobileCardIndex.value = 0
}

const goToPreviousPage = () => {
  if (canGoPrevious.value) {
    if (isMobile.value) {
      currentMobileCardIndex.value--
    } else {
      currentPage.value--
    }
  }
}

const goToNextPage = () => {
  if (canGoNext.value) {
    if (isMobile.value) {
      currentMobileCardIndex.value++
    } else {
      currentPage.value++
    }
  }
}

const getServiceIcon = (serviceType: string) => {
  const isActive = selectedService.value === serviceType
  const suffix = isActive ? '_xz' : ''
  return `/src/assets/AppService/${serviceType}${suffix}.svg`
}

// 检测是否为移动端
const checkIsMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 窗口大小变化处理
const handleResize = () => {
  checkIsMobile()
}

// 生命周期钩子
onMounted(() => {
  checkIsMobile()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>
<style scoped>
.fourth-page-container {
  background-image: url("../assets/fourth_background.png");
  width: calc(100% - 48px);
  height: 928px;
  margin: 0px 24px 0px 24px;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* margin-bottom: 120px; */
}

.header-text {
  padding-top: 120px;
  margin-bottom: 0px;
  color: var(--text-white, #FFF);
  font-family: "江城斜黑体", sans-serif;
  font-size: 48px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  letter-spacing: 4.8px;
}

.app-text {
  color: #2f7dfb;
}

.small-text {
  margin-top: 0;
  color: var(--text-4, #C9CDD4);
  text-align: center;
  font-family: JiangChengXieHei;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 1.6px;
  margin-bottom: 80px;
}

.service-text {
  color: var(--text-white, #FFF);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.service-container {
  display: flex;
  align-items: center;
  gap: 32px;
  margin-bottom: 40px;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 157px;
  height: 48px;
  margin: 10px 0;
  padding: 0 24px;
  border-radius:10px;
  box-sizing: border-box;
}

.service-item:hover {
  background-color: #2d3441;
  cursor: pointer;
}

.service-item.active {
  background-color: #424854;
}

.service-item.active .service-text {
  color: #2f7dfb;
}

.service-sub-container {
  width: calc(4 * 342px + 3 * 24px); /* 4张卡片宽度 + 3个间距 */
  overflow: hidden;
  margin-bottom: 80px;
}

.service-sub-item {
  display: flex;
  gap: 24px;
  width: fit-content;
  height: auto;
  transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.service-card {
  width: 342px;
  height: 320px;
  background-color: white;
  border-radius: 20px;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-sizing: border-box;
}

.card-sub {
  width: 322px;
  height: 300px;
  display: flex;
  flex-direction: column;
  background-image: url("../assets/service_item_background.png");
  border-radius: 10px;
}

.card-sub:hover {
  background-image: url("../assets/service_item_background_xt.png");
}

.card-sub:hover .card-icon {
  filter: brightness(0) saturate(100%) invert(42%) sepia(95%) saturate(1234%) hue-rotate(207deg) brightness(98%) contrast(96%);
}

.card-header {
  padding: 24px 24px;
  height: 252px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 26px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-title {
  width: 274px;
  height: 28px;
}

.card-title h3 {
  color: var(--text-1, #1D2129);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 20px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
}

.card-description {
  width: 274px;
  padding-bottom: 0px;
}

.card-description p {
  overflow: hidden;
  color: var(--text-3, #86909C);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 150% */
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  transition: -webkit-line-clamp 0.3s ease;
}

.card-header:hover .card-content {
  transform: translateY(-38px);
}

.card-header:hover .card-description p {
  -webkit-line-clamp: 4;
  line-clamp: 4;
}

.pagination-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
}

.page-turning,
.page-turning-right {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-turning:hover,
.page-turning-right:hover {
  transform: scale(1.1) translateX(-2px);
}

.page-turning-right:hover {
  transform: scale(1.1) translateX(2px);
}

.page-turning:active,
.page-turning-right:active {
  transform: scale(0.95);
}

.page-turning.disabled,
.page-turning-right.disabled {
  opacity: 0.3;
  cursor: not-allowed;
  pointer-events: none;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .fourth-page-container {
    background-image: url("../assets/fourth_background.png");
    width: calc(100% - 24px);
    height: auto;
    min-height: 100vh;
    margin: 40px 12px 0 12px;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 40px;
  }

  .header-text {
    padding-top: 60px;
    font-size: 32px;
    letter-spacing: 3.2px;
    margin-bottom: 0px;
  }

  .small-text {
    font-size: 14px;
    letter-spacing: 1.4px;
    margin-bottom: 40px;
  }

  .service-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 40px;
    width: calc(100% - 48px);
    max-width: 400px;
  }

  .service-item {
    width: 100%;
    height: 44px;
    padding: 0 16px;
    justify-content: center;
    border-radius: 8px;
  }

  .service-text {
    font-size: 16px;
  }

  .service-sub-container {
    width: calc(100% - 48px);
    max-width: 350px;
    overflow: hidden;
    margin-bottom: 40px;
  }

  .service-sub-item {
    display: flex;
    gap: 0;
    width: calc(100% * var(--total-cards));
    height: auto;
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .service-card {
    width: calc(100% / var(--total-cards));
    height: auto;
    min-height: 280px;
    margin-bottom: 20px;
    border-radius: 16px;
    padding: 8px;
    background-color: white;
    box-shadow: 0 4px 12px rgba(47, 125, 251, 0.15);
    flex-shrink: 0;
    box-sizing: border-box;
  }

  .card-sub {
    width: 100%;
    height: auto;
    min-height: 260px;
    padding: 20px;
    box-sizing: border-box;
  }

  .card-header {
    padding: 20px;
    height: 236px;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .card-content {
    margin-top: 60px;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .card-title {
    width: 100%;
    height: auto;
  }

  .card-title h3 {
    font-size: 18px;
    font-weight: 700;
    color: #1D2129;
    margin: 0;
    line-height: 24px;
  }

  .card-description {
    width: 100%;
    padding-right: 8px;
    flex: 1;
  }

  .card-description p {
    font-size: 14px;
    line-height: 20px;
    -webkit-line-clamp: 4;
    line-clamp: 4;
    color: #86909C;
    margin: 0;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }

  .card-sub {
    background-image: url("../assets/service_item_background.png");
    background-size: cover;
    background-position: center;
  }

  /* 移动端禁用桌面端悬停效果 */
  .card-sub:hover {
    background-image: url("../assets/service_item_background.png") !important;
  }

  .card-sub:hover .card-icon {
    filter: none !important;
  }

  .card-sub:hover .card-content {
    transform: none !important;
  }

  .card-sub:hover .card-description p {
    -webkit-line-clamp: 4 !important;
    line-clamp: 4 !important;
  }

  .pagination-container {
    gap: 20px;
  }

  .page-turning,
  .page-turning-right {
    width: 32px;
    height: 32px;
  }
}

/* 小屏幕手机适配 */
@media (max-width: 480px) {
  .fourth-page-container {
    margin: 32px 8px 0 8px;
    width: calc(100% - 16px);
    border-radius: 16px;
  }

  .header-text {
    padding-top: 40px;
    font-size: 28px;
    letter-spacing: 2.8px;
  }

  .small-text {
    font-size: 12px;
    letter-spacing: 1.2px;
    margin-bottom: 32px;
  }

  .service-container {
    width: calc(100% - 32px);
    gap: 12px;
  }

  .service-item {
    height: 40px;
    padding: 0 12px;
  }

  .service-text {
    font-size: 14px;
  }

  .service-sub-container {
    width: calc(100% - 32px);
    max-width: 320px;
  }

  .service-card {
    width: calc(100% / var(--total-cards));
    border-radius: 14px;
    padding: 6px;
  }

  .card-sub {
    min-height: 240px;
    padding: 16px;
  }

  .card-header {
    padding: 16px;
    height: 220px;
  }

  .card-content {
    margin-top: 56px;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .card-title {
    width: 100%;
    height: auto;
  }

  .card-title h3 {
    font-size: 16px;
    line-height: 22px;
    font-weight: 700;
    color: #1D2129;
    margin: 0;
  }

  .card-description {
    padding-right: 6px;
    flex: 1;
  }

  .card-description p {
    font-size: 13px;
    line-height: 18px;
    -webkit-line-clamp: 4;
    line-clamp: 4;
    color: #86909C;
    margin: 0;
  }

  /* 小屏幕禁用桌面端悬停效果 */
  .card-sub:hover {
    background-image: url("../assets/service_item_background.png") !important;
  }

  .card-sub:hover .card-icon {
    filter: none !important;
  }

  .card-sub:hover .card-content {
    transform: none !important;
  }

  .card-sub:hover .card-description p {
    -webkit-line-clamp: 4 !important;
    line-clamp: 4 !important;
  }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
  .fourth-page-container {
    margin: 28px 6px 0 6px;
    width: calc(100% - 12px);
    border-radius: 12px;
  }

  .header-text {
    font-size: 24px;
    letter-spacing: 2.4px;
  }

  .service-container {
    width: calc(100% - 24px);
    gap: 10px;
  }

  .service-item {
    height: 36px;
    padding: 0 10px;
  }

  .service-text {
    font-size: 13px;
  }

  .service-sub-container {
    width: calc(100% - 24px);
    max-width: 280px;
  }

  .service-card {
    width: calc(100% / var(--total-cards));
    border-radius: 12px;
    padding: 5px;
  }

  .card-sub {
    min-height: 220px;
    padding: 14px;
  }

  .card-header {
    padding: 14px;
    height: 200px;
  }

  .card-content {
    margin-top: 50px;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .card-title {
    width: 100%;
    height: auto;
  }

  .card-title h3 {
    font-size: 15px;
    line-height: 20px;
    font-weight: 700;
    color: #1D2129;
    margin: 0;
  }

  .card-description {
    padding-right: 4px;
    flex: 1;
  }

  .card-description p {
    font-size: 12px;
    line-height: 16px;
    -webkit-line-clamp: 4;
    line-clamp: 4;
    color: #86909C;
    margin: 0;
  }

  /* 超小屏幕禁用桌面端悬停效果 */
  .card-sub:hover {
    background-image: url("../assets/service_item_background.png") !important;
  }

  .card-sub:hover .card-icon {
    filter: none !important;
  }

  .card-sub:hover .card-content {
    transform: none !important;
  }

  .card-sub:hover .card-description p {
    -webkit-line-clamp: 4 !important;
    line-clamp: 4 !important;
  }
}
</style>